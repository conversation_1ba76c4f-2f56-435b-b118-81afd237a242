import React, { useCallback, useEffect, useRef, useState } from 'react';
import { TextInput, Stack, Group, Text, ScrollArea, Box, UnstyledButton, Center, Loader } from '@mantine/core';
import { IconSearch } from '@tabler/icons-react';
import { FloatingIndicator } from '@mantine/core';
import { NotificationApi } from '@api/NotificationApi';
import useInfiniteCursorFetch from '@core/hooks/useInfiniteCursorFetch';
import relativeTime from 'dayjs/plugin/relativeTime';
import dayjs from 'dayjs';
import classes from './NotificationBell.module.css';
import { useDebouncedValue, useIntersection } from '@mantine/hooks';
import EmptyBox from '@components/EmptyBox';
import NotificationItem from './NotificationItem';
import { DEFAULT_NOTIFICATION_DEBOUNCE_TIME, DEFAULT_NOTIFICATION_PAGE_SIZE } from './Constants';
import { refetchRequest } from '@common/utils/QueryUtils';
import useRegisterNotificationListener from '@core/hooks/useRegisterNotificationListener';

// Extend dayjs with the plugin
dayjs.extend(relativeTime);

const tabs = [
  { id: 'all', label: 'All', count: 0, color: 'gray' },
  { id: 'info', label: 'Info', count: 0, color: 'blue' },
  { id: 'success', label: 'Success', count: 0, color: 'green' },
  { id: 'warning', label: 'Warning', count: 0, color: 'yellow' },
];

const NotificationTab = ({ activeTab, setActiveTab }: { activeTab: string; setActiveTab: (tab: string) => void }) => {
  const [rootRef, setRootRef] = useState<HTMLDivElement | null>(null);
  const [controlsRefs, setControlsRefs] = useState<Record<string, HTMLButtonElement | null>>({});
  const setControlRef = (name: string) => (node: HTMLButtonElement) => {
    controlsRefs[name] = node;
    setControlsRefs(controlsRefs);
  };
  return (
    <Box className={classes.tabsContainer} ref={setRootRef}>
      {tabs.map((tab) => (
        <UnstyledButton
          key={tab.id}
          className={classes.tabControl}
          ref={setControlRef(tab.id)}
          onClick={() => setActiveTab(tab.id)}
          mod={{ active: activeTab === tab.id }}>
          <Group gap='xs'>
            <Text size='sm' className={classes.controlLabel}>
              {tab.label}
            </Text>
          </Group>
        </UnstyledButton>
      ))}
      <FloatingIndicator target={controlsRefs[activeTab]} parent={rootRef} className={classes.indicator} />
    </Box>
  );
};

export const NotificationDropDown = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [search] = useDebouncedValue(searchQuery, DEFAULT_NOTIFICATION_DEBOUNCE_TIME);
  const [activeTab, setActiveTab] = useState('all');
  const containerRef = useRef<HTMLDivElement>(null);
  const { entry, ref } = useIntersection({
    threshold: 1,
  });

  const {
    fetchNextPage,
    flatData: notifications,
    hasNextPage,
    isFetching,
    refetch,
  } = useInfiniteCursorFetch(NotificationApi.findAll({ pageSize: DEFAULT_NOTIFICATION_PAGE_SIZE }, { search }), {
    placeholderData: (prev) => prev,
    showLoading: false,
  });

  useRegisterNotificationListener(() => refetch());

  useEffect(() => {
    if (entry?.isIntersecting && hasNextPage && !isFetching) {
      fetchNextPage();
    }
  }, [entry?.isIntersecting, fetchNextPage, hasNextPage, isFetching, ref]);

  const onReadNotificationSuccess = useCallback(() => {
    refetchRequest(NotificationApi.getUnreadCount());
  }, []);

  return (
    <Box>
      <Box p='xs' style={{ borderBottom: '1px solid #E9ECEF' }}>
        <TextInput
          placeholder='Search notifications...'
          leftSection={<IconSearch size={16} />}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          mb='xs'
        />
        <NotificationTab activeTab={activeTab} setActiveTab={setActiveTab} />
      </Box>

      <Group justify='space-between' p='xs' style={{ borderBottom: '1px solid #E9ECEF' }}>
        <Text fw={600} size='sm'>
          Notifications
        </Text>
      </Group>

      <ScrollArea h={300} ref={containerRef}>
        {!isFetching && notifications?.length === 0 && (
          <Center h='100%'>
            <EmptyBox />
          </Center>
        )}
        {notifications && notifications.length > 0 && (
          <Stack gap={5} p={5}>
            {notifications.map((notification, index) => (
              <Box key={notification.id} ref={index === notifications?.length - 1 ? ref : undefined}>
                <NotificationItem notification={notification} onReadNotificationSuccess={onReadNotificationSuccess} />
              </Box>
            ))}
          </Stack>
        )}
        {isFetching && (
          <Center>
            <Loader color='blue' type='dots' />
          </Center>
        )}
      </ScrollArea>
    </Box>
  );
};

export default NotificationDropDown;
