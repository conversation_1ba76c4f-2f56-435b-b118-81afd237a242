import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';

interface NotificationState {
  unreadCount: number;
  isLoading: boolean;
}

const initialState: NotificationState = {
  unreadCount: 0,
  isLoading: false,
};

export const notificationSlice = createSlice({
  name: 'notification',
  initialState,
  reducers: {
    setUnreadCount: (state, action: PayloadAction<number>) => {
      state.unreadCount = action.payload;
    },
    incrementUnreadCount: (state) => {
      state.unreadCount += 1;
    },
    decrementUnreadCount: (state) => {
      if (state.unreadCount > 0) {
        state.unreadCount -= 1;
      }
    },
    resetUnreadCount: (state) => {
      state.unreadCount = 0;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
  },
});

export const { 
  setUnreadCount, 
  incrementUnreadCount, 
  decrementUnreadCount, 
  resetUnreadCount, 
  setLoading 
} = notificationSlice.actions;

export const getNotificationState = (state: RootState) => state.notification;

export default notificationSlice.reducer;
