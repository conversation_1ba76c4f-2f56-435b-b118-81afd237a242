import { NotificationSourceTypeEnum, NotificationTypeEnum } from '@common/constants/NotificationConstants';
import { z } from 'zod';

export const NotificationSearchModelSchema = z.object({
  search: z.string().optional(),
  types: z.array(z.nativeEnum(NotificationTypeEnum)).optional(),
  sourceType: z.array(z.nativeEnum(NotificationSourceTypeEnum)).optional(),
  isRead: z.boolean().optional(),
});

export type NotificationSearchModel = z.infer<typeof NotificationSearchModelSchema>;
