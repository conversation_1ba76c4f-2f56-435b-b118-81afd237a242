import { ROUTE_PATH } from '@common/utils/RouterUtils';
import { AclPermission } from '@models/AclPermission';
import {
  IconDatabaseCog,
  IconDatabaseSearch,
  IconFilterCog,
  IconInputSearch,
  IconMailCog,
  IconMailPlus,
  IconMailSearch,
  IconPencilCog,
  IconUserCheck,
  IconWebhook,
  IconServer,
  IconComponents,
  IconRobot,
  IconUrgent,
  IconUsersGroup,
  IconTemplate,
  IconSettingsUp,
  IconStackFront,
  IconDatabasePlus,
  IconLogs,
  IconProps,
  IconBrandTeams,
  IconDatabaseLeak,
  IconDeviceDesktopCog,
  IconDeviceDesktopCode,
  IconDeviceDesktopSearch,
  IconBrandTelegram,
  IconVariable,
} from '@tabler/icons-react';

export type AdminSelectionConfigType = {
  title: string;
  path: string;
  icon: React.ComponentType<IconProps>;
  children?: AdminSelectionConfigType[];
  requirePermissions?: AclPermission[];
};

export const AdminSelectionConfigs: AdminSelectionConfigType[] = [
  {
    title: 'Input Data',
    path: ROUTE_PATH.INPUT_DATA,
    icon: IconInputSearch,
    requirePermissions: [
      AclPermission.webHookView,
      AclPermission.databaseCollectView,
      AclPermission.emailCollectView,
      AclPermission.databaseThresholdConfigView,
    ],

    children: [
      { title: 'WebHook', path: ROUTE_PATH.WEBHOOK, icon: IconWebhook, requirePermissions: [AclPermission.webHookView] },
      {
        title: 'Database Collect',
        path: ROUTE_PATH.DATABASE_COLLECT,
        icon: IconDatabaseSearch,
        requirePermissions: [AclPermission.databaseCollectView],
      },
      {
        title: 'Email Collect',
        path: ROUTE_PATH.EMAIL_COLLECT,
        icon: IconMailSearch,
        requirePermissions: [AclPermission.emailCollectView],
      },
    ],
  },
  {
    title: 'Alert Config',
    path: ROUTE_PATH.ALERT_CONFIG,
    icon: IconUrgent,
    requirePermissions: [
      AclPermission.applicationManageView,
      AclPermission.serviceManageView,
      AclPermission.priorityConfigView,
      AclPermission.alertGroupConfigView,
      AclPermission.maintenanceTimeConfigView,
      AclPermission.filterAlertConfigView,
      AclPermission.telegramAlertConfig,
      AclPermission.modifyAlertConfigView,
    ],
    children: [
      {
        title: 'Service/Application',
        path: ROUTE_PATH.SERVICE_APPLICATION,
        icon: IconServer,
        requirePermissions: [AclPermission.applicationManageView, AclPermission.serviceManageView],
      },
      {
        title: 'Priority Config',
        path: ROUTE_PATH.PRIORITY_CONFIG,
        icon: IconStackFront,
        requirePermissions: [AclPermission.priorityConfigView],
      },
      {
        title: 'Alert Group Config',
        path: ROUTE_PATH.GROUP_CONFIG,
        icon: IconComponents,
        requirePermissions: [AclPermission.alertGroupConfigView],
      },
      {
        title: 'Maintenance Time Config',
        path: ROUTE_PATH.MAINTENANCE_TIME_CONFIG,
        icon: IconSettingsUp,
        requirePermissions: [AclPermission.maintenanceTimeConfigView],
      },
      {
        title: 'Filter Alert Config',
        path: ROUTE_PATH.FILTER_ALERT_CONFIG,
        icon: IconFilterCog,
        requirePermissions: [AclPermission.filterAlertConfigView],
      },
      {
        title: 'Modify Alert Config',
        path: ROUTE_PATH.MODIFY_ALERT_CONFIG,
        icon: IconPencilCog,
        requirePermissions: [AclPermission.modifyAlertConfigView],
      },
      {
        title: 'Teams Alert Config',
        path: ROUTE_PATH.TEAMS_ALERT_CONFIG,
        icon: IconBrandTeams,
        requirePermissions: [AclPermission.teamsAlertConfig],
      },
      {
        title: 'Telegram Alert Config',
        path: ROUTE_PATH.TELEGRAM_ALERT_CONFIG,
        icon: IconBrandTelegram,
        requirePermissions: [AclPermission.telegramAlertConfig],
      },
    ],
  },
  {
    title: 'Email Config',
    path: ROUTE_PATH.EMAIL_CONFIG,
    icon: IconMailCog,
    requirePermissions: [AclPermission.emailPartnerView, AclPermission.emailConnectionView, AclPermission.emailTemplateView],

    children: [
      {
        title: 'Partner Management',
        path: ROUTE_PATH.PARTNER_MANAGEMENT,
        icon: IconUsersGroup,
        requirePermissions: [AclPermission.emailPartnerView],
      },
      {
        title: 'Email Connection',
        path: ROUTE_PATH.EMAIL_CONNECTION,
        icon: IconMailPlus,
        requirePermissions: [AclPermission.emailConnectionView],
      },
      { title: 'Email Template', path: ROUTE_PATH.EMAIL_TEMPLATE, icon: IconTemplate, requirePermissions: [AclPermission.emailTemplateView] },
    ],
  },
  {
    title: 'Database Config',
    path: ROUTE_PATH.DATABASE_CONFIG,
    icon: IconDatabaseCog,
    requirePermissions: [
      AclPermission.databaseConnectionView,
      AclPermission.executionGroupView,
      AclPermission.executionView,
      AclPermission.databaseThresholdConfigView,
    ],
    children: [
      {
        title: 'Database Connection',
        path: ROUTE_PATH.DATABASE_CONNECTION,
        icon: IconDatabasePlus,
        requirePermissions: [AclPermission.databaseConnectionView],
      },
      {
        title: 'Database Threshold',
        path: ROUTE_PATH.DATABASE_THRESHOLD,
        icon: IconDatabaseLeak,
        requirePermissions: [AclPermission.databaseThresholdConfigView],
      },
    ],
  },
  {
    title: 'Permission Management',
    path: ROUTE_PATH.PERMISSION_MANAGERMENT,
    icon: IconUserCheck,
    requirePermissions: [AclPermission.userManageView, AclPermission.roleManageView],
  },
  {
    title: 'Custom Object',
    path: ROUTE_PATH.CUSTOM_OBJECT,
    icon: IconRobot,
    requirePermissions: [AclPermission.customObjectView],
  },
  {
    title: 'System Log',
    path: ROUTE_PATH.SYS_LOG,
    icon: IconLogs,
    requirePermissions: [AclPermission.syslogView],
  },
  {
    title: 'Execution',
    path: ROUTE_PATH.EXECUTION_CONFIG_ROOT,
    icon: IconDeviceDesktopCog,
    requirePermissions: [AclPermission.executionView, AclPermission.executionGroupView, AclPermission.executionHistoryView],
    children: [
      {
        title: 'Execution Config',
        path: ROUTE_PATH.EXECUTION_CONFIG,
        icon: IconDeviceDesktopCode,
        requirePermissions: [AclPermission.executionView, AclPermission.executionGroupView],
      },
      {
        title: 'Execution History',
        path: ROUTE_PATH.EXECUTION_HISTORY,
        requirePermissions: [AclPermission.executionHistoryView],
        icon: IconDeviceDesktopSearch,
      },
    ],
  },
  {
    title: 'Variable',
    path: ROUTE_PATH.VARIABLE,
    icon: IconVariable,
    requirePermissions: [AclPermission.variableView],
  },
];
