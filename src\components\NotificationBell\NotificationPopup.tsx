import React, { useState } from 'react';
import { TextInput, <PERSON><PERSON>, Badge, Stack, Group, Text, ActionIcon, ScrollArea, Button, Box } from '@mantine/core';
import { IconSearch, IconBell, IconCheck, IconAlertTriangle, IconChevronDown } from '@tabler/icons-react';
import { NotificationTypeEnum } from '@common/constants/NotificationConstants';
import { NotificationApi } from '@api/NotificationApi';
import useInfiniteCursorFetch from '@core/hooks/useInfiniteCursorFetch';
import { DEFAULT_TASK_PAGE_SIZE } from '@pages/events/Contants';
import relativeTime from 'dayjs/plugin/relativeTime';
import dayjs from 'dayjs';
import classes from './NotificationBell.module.css';

// Extend dayjs with the plugin
dayjs.extend(relativeTime);

const getNotificationIcon = (type: NotificationTypeEnum) => {
  switch (type) {
    case NotificationTypeEnum.INFO:
      return <IconBell size={16} color='#339AF0' />;
    case NotificationTypeEnum.CRITICAL:
      return <IconCheck size={16} color='yellow.3' />;
    case NotificationTypeEnum.WARNING:
      return <IconAlertTriangle size={16} color='yellow.3' />;
    default:
      return <IconBell size={16} color='#339AF0' />;
  }
};

export const NotificationPopup = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState<string | null>('all');
  const { flatData: notifications } = useInfiniteCursorFetch(NotificationApi.findAll({ pageSize: DEFAULT_TASK_PAGE_SIZE }), {
    showLoading: false,
  });

  const handleMarkAllAsRead = () => {
    // Implement mark all as read functionality
    console.info('Mark all as read');
  };

  return (
    <Box>
      <Box p='md' style={{ borderBottom: '1px solid #E9ECEF' }}>
        <TextInput
          placeholder='Search notifications...'
          leftSection={<IconSearch size={16} />}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          mb='md'
        />

        <Tabs value={activeTab} onChange={setActiveTab}>
          <Tabs.List>
            <Tabs.Tab
              value='all'
              rightSection={
                <Badge size='xs' variant='light'>
                  0
                </Badge>
              }>
              All
            </Tabs.Tab>
            <Tabs.Tab
              value='info'
              rightSection={
                <Badge size='xs' variant='light' color='blue'>
                  0
                </Badge>
              }>
              Info
            </Tabs.Tab>
            <Tabs.Tab
              value='success'
              rightSection={
                <Badge size='xs' variant='light' color='green'>
                  0
                </Badge>
              }>
              Success
            </Tabs.Tab>
            <Tabs.Tab
              value='warning'
              rightSection={
                <Badge size='xs' variant='light' color='yellow'>
                  0
                </Badge>
              }>
              Warning
            </Tabs.Tab>
          </Tabs.List>
        </Tabs>
      </Box>

      <Group justify='space-between' p='md' style={{ borderBottom: '1px solid #E9ECEF' }}>
        <Text fw={600} size='sm'>
          Notifications
        </Text>
        <Button variant='subtle' size='xs' onClick={handleMarkAllAsRead}>
          Mark all as read
        </Button>
      </Group>

      <ScrollArea h={300}>
        <Stack gap={2} p={2}>
          {notifications?.map((notification) => (
            <Box key={notification.id} p='md' className={`${classes.notificationItem} ${notification.isRead ? classes.isRead : ''}`}>
              <Group align='flex-start' gap='sm'>
                {getNotificationIcon(notification.type)}
                <Box style={{ flex: 1 }}>
                  <Group justify='space-between' align='flex-start'>
                    <Text fw={600} size='sm' c={notification.isRead ? 'dimmed' : 'dark'}>
                      {notification.title}
                    </Text>
                    <ActionIcon variant='subtle' size='xs'>
                      <IconChevronDown size={12} />
                    </ActionIcon>
                  </Group>
                  <Text size='xs' c='dimmed' mt={2}>
                    {notification.content}
                  </Text>
                  <Text size='xs' c='dimmed' mt={4}>
                    {notification.createdDate.fromNow()}
                  </Text>
                </Box>
              </Group>
            </Box>
          ))}
        </Stack>
      </ScrollArea>
    </Box>
  );
};

export default NotificationPopup;
