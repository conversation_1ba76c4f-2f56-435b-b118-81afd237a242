import React, { useState } from 'react';
import { TextInput, Stack, Group, Text, ActionIcon, ScrollArea, Button, Box, UnstyledButton } from '@mantine/core';
import { IconSearch, IconBell, IconCheck, IconAlertTriangle, IconChevronDown } from '@tabler/icons-react';
import { FloatingIndicator } from '@mantine/core';
import { NotificationTypeEnum } from '@common/constants/NotificationConstants';
import { NotificationApi } from '@api/NotificationApi';
import useInfiniteCursorFetch from '@core/hooks/useInfiniteCursorFetch';
import { DEFAULT_TASK_PAGE_SIZE } from '@pages/events/Contants';
import relativeTime from 'dayjs/plugin/relativeTime';
import dayjs from 'dayjs';
import classes from './NotificationBell.module.css';

// Extend dayjs with the plugin
dayjs.extend(relativeTime);

const getNotificationIcon = (type: NotificationTypeEnum) => {
  switch (type) {
    case NotificationTypeEnum.INFO:
      return <IconBell size={16} color='#339AF0' />;
    case NotificationTypeEnum.CRITICAL:
      return <IconCheck size={16} color='yellow.3' />;
    case NotificationTypeEnum.WARNING:
      return <IconAlertTriangle size={16} color='yellow.3' />;
    default:
      return <IconBell size={16} color='#339AF0' />;
  }
};

export const NotificationPopup = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [rootRef, setRootRef] = useState<HTMLDivElement | null>(null);
  const [controlsRefs, setControlsRefs] = useState<Record<string, HTMLButtonElement | null>>({});

  const { flatData: notifications } = useInfiniteCursorFetch(NotificationApi.findAll({ pageSize: DEFAULT_TASK_PAGE_SIZE }), {
    showLoading: false,
  });

  const setControlRef = (name: string) => (node: HTMLButtonElement) => {
    controlsRefs[name] = node;
    setControlsRefs(controlsRefs);
  };

  const tabs = [
    { id: 'all', label: 'All', count: 0, color: 'gray' },
    { id: 'info', label: 'Info', count: 0, color: 'blue' },
    { id: 'success', label: 'Success', count: 0, color: 'green' },
    { id: 'warning', label: 'Warning', count: 0, color: 'yellow' },
  ];

  const handleMarkAllAsRead = () => {
    // Implement mark all as read functionality
    console.info('Mark all as read');
  };

  return (
    <Box>
      <Box p='xs' style={{ borderBottom: '1px solid #E9ECEF' }}>
        <TextInput
          placeholder='Search notifications...'
          leftSection={<IconSearch size={16} />}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          mb='md'
        />

        <Box className={classes.tabsContainer} ref={setRootRef}>
          {tabs.map((tab) => (
            <UnstyledButton
              key={tab.id}
              className={classes.tabControl}
              ref={setControlRef(tab.id)}
              onClick={() => setActiveTab(tab.id)}
              mod={{ active: activeTab === tab.id }}>
              <Group gap='xs'>
                <Text size='sm' className={classes.controlLabel}>
                  {tab.label}
                </Text>
              </Group>
            </UnstyledButton>
          ))}
          <FloatingIndicator target={controlsRefs[activeTab]} parent={rootRef} className={classes.indicator} />
        </Box>
      </Box>

      <Group justify='space-between' p='xs' style={{ borderBottom: '1px solid #E9ECEF' }}>
        <Text fw={600} size='sm'>
          Notifications
        </Text>
        <Button variant='subtle' size='xs' onClick={handleMarkAllAsRead}>
          Mark all as read
        </Button>
      </Group>

      <ScrollArea h={300}>
        <Stack gap={5} p={5}>
          {notifications?.map((notification) => (
            <Box key={notification.id} p='md' className={`${classes.notificationItem} ${notification.isRead ? classes.isRead : ''}`}>
              <Group align='flex-start' gap='sm'>
                {getNotificationIcon(notification.type)}
                <Box style={{ flex: 1 }}>
                  <Group justify='space-between' align='flex-start'>
                    <Text fw={600} size='sm' c={notification.isRead ? 'dimmed' : 'dark'}>
                      {notification.title}
                    </Text>
                    <ActionIcon variant='subtle' size='xs'>
                      <IconChevronDown size={12} />
                    </ActionIcon>
                  </Group>
                  <Text size='xs' c='dimmed' mt={2}>
                    {notification.content}
                  </Text>
                  <Text size='xs' c='dimmed' mt={4}>
                    {notification.createdDate.fromNow()}
                  </Text>
                </Box>
              </Group>
            </Box>
          ))}
        </Stack>
      </ScrollArea>
    </Box>
  );
};

export default NotificationPopup;
