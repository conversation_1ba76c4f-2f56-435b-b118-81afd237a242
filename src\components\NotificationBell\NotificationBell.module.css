.bellIndicator {
  background-color: '#FF6B9D';
  border: '2px solid white';
  font-size: '10px';
  font-weight: 600;
  min-width: '16px';
  min-height: '16px';
}
.bellButton {
  background-color: white;
  border: 1px solid var(--mantine-color-gray-4);
  border-radius: '50%';
  transition: all 0.2s ease;
}
.bellButton:hover {
  background-color: '#E9ECEF';
  border-color: '#DEE2E6';
  transform: 'scale(1.05)';
  box-shadow: '0 2px 8px rgba(0, 0, 0, 0.1)';
}
.bellButton:active {
  transform: 'scale(0.95)';
}
.notificationDropdown {
  padding: 0;
  border: 1px solid white;
}
.notificationItem {
  border: 1px solid var(--mantine-color-blue-2);
  cursor: pointer;
  transition: background-color 0.2s ease;
  background-color: var(--mantine-color-blue-0);
  border-radius: var(--mantine-radius-default);
}
.notificationItem:hover {
  background-color: '#E9ECEF';
}
.notificationItem.isRead {
  background-color: transparent;
}

