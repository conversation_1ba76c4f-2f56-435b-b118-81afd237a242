/* eslint-disable no-console */
import React, { useEffect, useRef } from 'react';
import { Route, RouterProps, Routes } from 'react-router-dom';
import { RoutePropsType, getHeaderLink, routeConfigs } from 'routes';
import AppShell from '@components/appShell';
import HomePage from './base/HomePage';
import ForbiddenPage from './base/ForbiddenPage';
import ErrorPage from './base/ErrorPage';
import { useAppDispatch, useAppSelector } from '@store';
import { currentUserSlice, getCurrentUser } from '@slices/CurrentUserSlice';
import LoginPage from './auth/Login';
import UserNotFoundPage from './base/UserNotFoundPage';
import { refreshAuthTokens, startRefreshTokenInterval } from '@common/utils/AuthenticateUtils';
import { getWebSocketService } from '@core/api/WebSocketService';

function Index() {
  const dispatch = useAppDispatch();
  const userState = useAppSelector(getCurrentUser);
  const websocketRef = useRef<ReturnType<typeof getWebSocketService> | null>(null);

  useEffect(() => {
    dispatch(currentUserSlice.actions.fetchData());
    refreshAuthTokens();
    startRefreshTokenInterval();
  }, [dispatch]);

  useEffect(() => {
    if (userState.userInfo?.isActive) {
      const websocketService = websocketRef.current ? websocketRef.current : getWebSocketService();
      if (websocketService.isWebSocketConnected()) {
        return;
      }
      websocketService.connect();
      websocketRef.current = websocketService;
    }

    return () => {
      if (websocketRef.current && websocketRef.current.isWebSocketConnected()) {
        websocketRef.current.disconnect();
      }
    };
  }, [userState.userInfo]);

  const generateRoute = (routeConfig: RoutePropsType, index: number) => {
    const { children, ...rest } = routeConfig;
    const routerProps = rest as RouterProps;
    return (
      <Route key={index} {...routerProps}>
        {children &&
          children.map((x, key) => {
            return generateRoute(x, key);
          })}
      </Route>
    );
  };
  if (userState.isFetching) {
    return null;
  }
  if (!userState || !userState.userInfo || location.pathname === '/login') {
    return (
      <Routes>
        <Route path='/login' element={<LoginPage />} />
      </Routes>
    );
  }

  if (!userState.userInfo?.isActive) {
    return <UserNotFoundPage />;
  }
  return (
    <>
      <AppShell routers={[]} headerLinks={getHeaderLink()}>
        <Routes>
          <Route path='/' element={<HomePage />} />
          <Route path='/403' element={<ForbiddenPage />} />
          <Route path='*' element={<ErrorPage />} />
          {routeConfigs.map((x, key) => generateRoute(x, key))}
        </Routes>
      </AppShell>
    </>
  );
}
export default Index;
