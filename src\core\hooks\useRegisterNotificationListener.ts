import { getWebSocketService } from '@core/api/WebSocketService';
import { Notification } from '@core/schema/Notification';
import { useEffect } from 'react';

const useRegisterNotificationListener = (callback: (notification: Notification) => void) => {
  const websocketService = getWebSocketService();
  useEffect(() => {
    websocketService.registerNotificationHandler(callback);
    return () => {
      websocketService.unregisterNotificationHandler(callback);
    };
  }, [callback, websocketService]);
};

export default useRegisterNotificationListener;
