import { BaseURL } from '@common/constants/BaseUrl';
import { createResponseSchema } from '@core/schema';
import { z } from 'zod';
import { createRequest } from './Utils';

export class NotificationApi {
  static getUnreadCount() {
    return createRequest({
      url: `${BaseURL.notification}/unread-count`,
      method: 'GET',
      schema: createResponseSchema(z.number()),
    });
  }

  static markAsRead(notificationId: string) {
    return createRequest({
      url: `${BaseURL.notification}/${notificationId}/read`,
      method: 'PUT',
    });
  }

  static markAllAsRead() {
    return createRequest({
      url: `${BaseURL.notification}/mark-all-read`,
      method: 'PUT',
    });
  }
}
