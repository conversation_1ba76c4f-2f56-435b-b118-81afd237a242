import { BaseURL } from '@common/constants/BaseUrl';
import { createCursorPageSchema, createResponseSchema } from '@core/schema';
import { z } from 'zod';
import { createRequest } from './Utils';
import { NotificationSearchModel } from '@models/NotificationSearchModel';
import { NotificationSchema } from '@core/schema/Notification';
import { NotificationCursor, NotificationCursorSchema } from '@core/schema/NotificationCursor';
import { CursorPagingRequestModel } from '@models/CursorPagingRequestModel';

export class NotificationApi {
  static getUnreadCount() {
    return createRequest({
      url: `${BaseURL.notification}/unread-count`,
      method: 'GET',
      schema: createResponseSchema(z.number()),
    });
  }

  static markAsRead(notificationId: string) {
    return createRequest({
      url: `${BaseURL.notification}/${notificationId}/read`,
      method: 'PUT',
    });
  }

  static findAll(cursor: CursorPagingRequestModel<NotificationCursor>, searchRequest?: NotificationSearchModel) {
    return createRequest({
      url: `${BaseURL.notification}`,
      method: 'GET',
      schema: createResponseSchema(createCursorPageSchema(NotificationSchema, NotificationCursorSchema)),
      params: { ...searchRequest, ...cursor },
    });
  }
}
