import React, { useState, useEffect } from 'react';
import { ActionIcon, Indicator } from '@mantine/core';
import { IconBell } from '@tabler/icons-react';
import { NotificationApi } from '@api/NotificationApi';
import useFetch from '@core/hooks/useFetch';

interface NotificationBellProps {
  onClick?: () => void;
}

 const NotificationBell: React.FC<NotificationBellProps> = ({ onClick }) => {
  const [count, setCount] = useState(0);

  // Fetch initial unread count
  const { data: unreadCountData, refetch } = useFetch(NotificationApi.getUnreadCount, {
    errorNotification: false, // Don't show error notifications for this
  });

  useEffect(() => {
    if (unreadCountData?.data !== undefined) {
      setCount(unreadCountData.data);
    }
  }, [unreadCountData]);

  // Function to refresh count from API
  const refreshCount = () => {
    refetch();
  };

  const handleClick = () => {
    if (onClick) {
      onClick();
    }
    // Optionally refresh count when bell is clicked
    refreshCount();
  };

  return (
    <Indicator
      disabled={count === 0}
      label={count > 99 ? '99+' : count.toString()}
      size={16}
      color='#FF6B9D'
      offset={7}
      styles={{
        indicator: {
          backgroundColor: '#FF6B9D',
          border: '2px solid white',
          fontSize: '10px',
          fontWeight: 600,
          minWidth: '16px',
          height: '16px',
        },
      }}>
      <ActionIcon
        variant='light'
        size='lg'
        radius='xl'
        onClick={handleClick}
        aria-label={`Notifications (${count} unread)`}
        style={{
          backgroundColor: '#F8F9FA',
          border: '1px solid #E9ECEF',
          borderRadius: '50%',
        }}>
        <IconBell size='1.1rem' stroke={1.5} color='#495057' />
      </ActionIcon>
    </Indicator>
  );
};

export default NotificationBell;
