import React from 'react';
import { ActionIcon, Indicator } from '@mantine/core';
import { IconBell } from '@tabler/icons-react';

interface NotificationBellProps {
  count?: number;
  onClick?: () => void;
}

export const NotificationBell: React.FC<NotificationBellProps> = ({ count = 0, onClick }) => {
  return (
    <Indicator 
      disabled={count === 0} 
      label={count > 99 ? '99+' : count.toString()} 
      size={16}
      color="red"
      offset={7}
    >
      <ActionIcon
        variant="subtle"
        size="lg"
        onClick={onClick}
        aria-label={`Notifications (${count} unread)`}
      >
        <IconBell size="1.2rem" stroke={1.5} />
      </ActionIcon>
    </Indicator>
  );
};

export default NotificationBell;
