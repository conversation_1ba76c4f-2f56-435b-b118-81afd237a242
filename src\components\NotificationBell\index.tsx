import React, { useState, useEffect } from 'react';



import { ActionIcon, Indicator } from '@mantine/core';



import { IconBell } from '@tabler/icons-react';



import { NotificationApi } from '@api/NotificationApi';



import useFetch from '@core/hooks/useFetch';



interface NotificationBellProps {

  onClick?: () => void;

}



export const NotificationBell: React.FC<NotificationBellProps> = ({ onClick }) => {

  const [count, setCount] = useState(0);



  // Fetch initial unread count



  const { data: unreadCountData, refetch } = useFetch(NotificationApi.getUnreadCount, {

    errorNotification: false, // Don't show error notifications for this

  });



  useEffect(() => {

    if (unreadCountData?.data !== undefined) {

      setCount(unreadCountData.data);

    }

  }, [unreadCountData]);



  // Function to refresh count from API



  const refreshCount = () => {

    refetch();

  };



  const handleClick = () => {

    if (onClick) {

      onClick();

    }



    // Optionally refresh count when bell is clicked



    refreshCount();

  };



  return (

    <Indicator disabled={count === 0} label={count > 99 ? '99+' : count.toString()} size={16} color='red' offset={7}>

      <ActionIcon variant='subtle' size='lg' onClick={handleClick} aria-label={`Notifications (${count} unread)`}>

        <IconBell size='1.2rem' stroke={1.5} />

      </ActionIcon>

    </Indicator>

  );

};



export default NotificationBell;

